import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/entities/user.entity';
import { Role } from '@/entities/role.entity';
import { Team } from '@/entities/team.entity';
import { Department } from '@/entities/department.entity';
import { RoleType } from '@/common/constants';

export interface UserContext {
  id: string;
  username: string;
  email: string;
  role?: Role;
  team?: Team;
  department?: Department;
  permissions: string[];
}

@Injectable()
export class UserContextService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async getUserContext(userId: string): Promise<UserContext | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role', 'team', 'department', 'menuAssignments', 'menuAssignments.permissions'],
    });

    if (!user) {
      return null;
    }

    // Get unique permissions from all menu assignments
    const permissions = new Set<string>();
    user.menuAssignments?.forEach((menuAssignment) => {
      menuAssignment.permissions?.forEach((permission) => {
        permissions.add(permission.action);
      });
    });

    return {
      id: user.id,
      username: user.username,
      email: user?.email || '',
      role: user.role,
      team: user.team,
      department: user.department,
      permissions: Array.from(permissions),
    };
  }

  canManageRole(userContext: UserContext, roleId: string): boolean {
    // Super admin can manage all roles
    if (userContext.username === 'super_admin') {
      return true;
    }

    // Manager can manage roles below their level
    if (userContext.role?.name === RoleType.MANAGER) {
      return true;
    }

    return false;
  }

  canManageDepartment(userContext: UserContext, departmentId: string): boolean {
    // Super admin can manage all departments
    if (userContext.username === 'super_admin') {
      return true;
    }

    // Manager can manage all departments
    if (userContext.role?.name === RoleType.MANAGER) {
      return true;
    }

    // Team leader can only manage their own department
    if (userContext.role?.name === RoleType.TEAM_LEADER) {
      return userContext.department?.id === departmentId;
    }

    return false;
  }

  canManageTeam(userContext: UserContext, teamId: string, departmentId?: string): boolean {
    // Super admin can manage all teams
    if (userContext.username === 'super_admin') {
      return true;
    }

    // Manager can manage all teams
    if (userContext.role?.name === RoleType.MANAGER) {
      return true;
    }

    // Team leader can only manage teams in their department
    if (userContext.role?.name === RoleType.TEAM_LEADER) {
      if (departmentId && userContext.department?.id !== departmentId) {
        return false;
      }
      return userContext.team?.id === teamId;
    }

    return false;
  }

  canManageUser(userContext: UserContext, targetUserId: string): boolean {
    // Super admin can manage all users
    if (userContext.username === 'super_admin') {
      return true;
    }

    // Users cannot manage themselves through this method
    if (userContext.id === targetUserId) {
      return false;
    }

    // Manager can manage all users
    if (userContext.role?.name === RoleType.MANAGER) {
      return true;
    }

    // Team leader can manage team members in their department
    if (userContext.role?.name === RoleType.TEAM_LEADER) {
      // This would require additional logic to check if target user is in same department/team
      return true; // Simplified for now
    }

    return false;
  }

  hasPermission(userContext: UserContext, permission: string): boolean {
    return userContext.permissions.includes(permission);
  }

  isSuperAdmin(userContext: UserContext): boolean {
    return userContext.username === 'super_admin';
  }

  isManager(userContext: UserContext): boolean {
    return userContext.role?.name === RoleType.MANAGER;
  }

  isTeamLeader(userContext: UserContext): boolean {
    return userContext.role?.name === RoleType.TEAM_LEADER;
  }

  isTeamMember(userContext: UserContext): boolean {
    return userContext.role?.name === RoleType.TEAM_MEMBER;
  }

  async getAssignableRoles(currentRole?: Role): Promise<Role[]> {
    // Super admin can assign all roles
    if (!currentRole) {
      return this.roleRepository.find();
    }

    // Manager can assign roles below their level
    if (currentRole.name === RoleType.MANAGER) {
      return this.roleRepository.createQueryBuilder('role')
        .where('role.level >= :level', { level: currentRole.level })
        .getMany();
    }

    // Team leader can only assign team member roles
    if (currentRole.name === RoleType.TEAM_LEADER) {
      return this.roleRepository.find({
        where: { name: RoleType.TEAM_MEMBER }
      });
    }

    return [];
  }
}
