import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { QueueService } from './queue.service';
import { CustomLoggerService } from '../logger/logger.service';
import { QUEUE_NAMES, JOB_PRIORITIES } from './queue.constants';

describe('QueueService', () => {
  let service: QueueService;
  let importExportQueue: jest.Mocked<Queue>;
  let fileProcessingQueue: jest.Mocked<Queue>;
  let emailQueue: jest.Mocked<Queue>;
  let analyticsQueue: jest.Mocked<Queue>;
  let logger: jest.Mocked<CustomLoggerService>;

  const mockJob = {
    id: '12345',
    name: 'test-job',
    data: { test: 'data' },
    opts: {},
    progress: jest.fn().mockReturnValue(50),
    delay: 0,
    timestamp: Date.now(),
    attemptsMade: 1,
    failedReason: null,
    stacktrace: null,
    returnvalue: null,
    finishedOn: null,
    processedOn: null,
    remove: jest.fn(),
    retry: jest.fn(),
    getState: jest.fn().mockResolvedValue('active'),
  } as unknown as Job;

  beforeEach(async () => {
    const mockQueue = {
      add: jest.fn(),
      getJob: jest.fn(),
      getWaiting: jest.fn(),
      getActive: jest.fn(),
      getCompleted: jest.fn(),
      getFailed: jest.fn(),
      getDelayed: jest.fn(),
      clean: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
    };

    const mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QueueService,
        {
          provide: getQueueToken(QUEUE_NAMES.IMPORT_EXPORT),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken(QUEUE_NAMES.FILE_PROCESSING),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken(QUEUE_NAMES.EMAIL_NOTIFICATIONS),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken(QUEUE_NAMES.DATA_ANALYTICS),
          useValue: mockQueue,
        },
        {
          provide: CustomLoggerService,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<QueueService>(QueueService);
    importExportQueue = module.get(getQueueToken(QUEUE_NAMES.IMPORT_EXPORT));
    fileProcessingQueue = module.get(getQueueToken(QUEUE_NAMES.FILE_PROCESSING));
    emailQueue = module.get(getQueueToken(QUEUE_NAMES.EMAIL_NOTIFICATIONS));
    analyticsQueue = module.get(getQueueToken(QUEUE_NAMES.DATA_ANALYTICS));
    logger = module.get(CustomLoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addImportExportJob', () => {
    it('should add job to import/export queue with default options', async () => {
      importExportQueue.add.mockResolvedValue(mockJob);

      const result = await service.addImportExportJob('test-job', { test: 'data' });

      expect(importExportQueue.add).toHaveBeenCalledWith('test-job', { test: 'data' }, {
        priority: JOB_PRIORITIES.NORMAL,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      });

      expect(result).toBe(mockJob);
      expect(logger.info).toHaveBeenCalledWith(
        'Added import/export job: test-job with ID: 12345',
        'QueueService',
        { jobType: 'test-job', jobId: '12345', data: { test: 'data' } }
      );
    });

    it('should add job with custom options', async () => {
      importExportQueue.add.mockResolvedValue(mockJob);

      const customOptions = {
        priority: JOB_PRIORITIES.HIGH,
        attempts: 5,
        delay: 1000,
      };

      await service.addImportExportJob('test-job', { test: 'data' }, customOptions);

      expect(importExportQueue.add).toHaveBeenCalledWith('test-job', { test: 'data' }, {
        priority: JOB_PRIORITIES.HIGH,
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
        delay: 1000,
      });
    });

    it('should handle errors when adding job', async () => {
      const error = new Error('Queue error');
      importExportQueue.add.mockRejectedValue(error);

      await expect(service.addImportExportJob('test-job', { test: 'data' })).rejects.toThrow('Queue error');

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to add import/export job: test-job',
        error.stack,
        'QueueService'
      );
    });
  });

  describe('addFileProcessingJob', () => {
    it('should add job to file processing queue with correct defaults', async () => {
      fileProcessingQueue.add.mockResolvedValue(mockJob);

      await service.addFileProcessingJob('process-file', { filePath: '/tmp/test.txt' });

      expect(fileProcessingQueue.add).toHaveBeenCalledWith('process-file', { filePath: '/tmp/test.txt' }, {
        priority: JOB_PRIORITIES.HIGH,
        attempts: 2,
        removeOnComplete: 5,
        removeOnFail: 20,
      });
    });
  });

  describe('addEmailJob', () => {
    it('should add job to email queue with correct defaults', async () => {
      emailQueue.add.mockResolvedValue(mockJob);

      await service.addEmailJob('send-email', { to: '<EMAIL>' });

      expect(emailQueue.add).toHaveBeenCalledWith('send-email', { to: '<EMAIL>' }, {
        priority: JOB_PRIORITIES.NORMAL,
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: 20,
        removeOnFail: 100,
      });
    });
  });

  describe('addAnalyticsJob', () => {
    it('should add job to analytics queue with correct defaults', async () => {
      analyticsQueue.add.mockResolvedValue(mockJob);

      await service.addAnalyticsJob('calculate-stats', { userId: 1 });

      expect(analyticsQueue.add).toHaveBeenCalledWith('calculate-stats', { userId: 1 }, {
        priority: JOB_PRIORITIES.LOW,
        attempts: 2,
        removeOnComplete: 5,
        removeOnFail: 10,
      });
    });
  });

  describe('getJob', () => {
    it('should get job from correct queue', async () => {
      importExportQueue.getJob.mockResolvedValue(mockJob);

      const result = await service.getJob(QUEUE_NAMES.IMPORT_EXPORT, '12345');

      expect(importExportQueue.getJob).toHaveBeenCalledWith('12345');
      expect(result).toBe(mockJob);
    });

    it('should return null when job not found', async () => {
      importExportQueue.getJob.mockResolvedValue(null);

      const result = await service.getJob(QUEUE_NAMES.IMPORT_EXPORT, '12345');

      expect(result).toBeNull();
    });

    it('should handle errors when getting job', async () => {
      const error = new Error('Queue error');
      importExportQueue.getJob.mockRejectedValue(error);

      await expect(service.getJob(QUEUE_NAMES.IMPORT_EXPORT, '12345')).rejects.toThrow('Queue error');

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to get job 12345 from queue import-export',
        error.stack,
        'QueueService'
      );
    });
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      const mockWaitingJobs = [mockJob];
      const mockActiveJobs = [mockJob];
      const mockCompletedJobs = [mockJob, mockJob];
      const mockFailedJobs = [mockJob];
      const mockDelayedJobs = [];

      importExportQueue.getWaiting.mockResolvedValue(mockWaitingJobs);
      importExportQueue.getActive.mockResolvedValue(mockActiveJobs);
      importExportQueue.getCompleted.mockResolvedValue(mockCompletedJobs);
      importExportQueue.getFailed.mockResolvedValue(mockFailedJobs);
      importExportQueue.getDelayed.mockResolvedValue(mockDelayedJobs);

      const result = await service.getQueueStats(QUEUE_NAMES.IMPORT_EXPORT);

      expect(result).toEqual({
        name: QUEUE_NAMES.IMPORT_EXPORT,
        counts: {
          waiting: 1,
          active: 1,
          completed: 2,
          failed: 1,
          delayed: 0,
          paused: 0,
        },
        jobs: {
          waiting: expect.any(Array),
          active: expect.any(Array),
          completed: expect.any(Array),
          failed: expect.any(Array),
        },
      });
    });

    it('should handle errors when getting queue stats', async () => {
      const error = new Error('Queue error');
      importExportQueue.getWaiting.mockRejectedValue(error);

      await expect(service.getQueueStats(QUEUE_NAMES.IMPORT_EXPORT)).rejects.toThrow('Queue error');

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to get stats for queue import-export',
        error.stack,
        'QueueService'
      );
    });
  });

  describe('getAllQueuesStats', () => {
    it('should return statistics for all queues', async () => {
      // Mock getQueueStats for each queue
      jest.spyOn(service, 'getQueueStats').mockResolvedValue({
        name: 'test-queue',
        counts: {
          waiting: 1,
          active: 1,
          completed: 2,
          failed: 1,
          delayed: 0,
          paused: 0,
        },
        jobs: {
          waiting: [],
          active: [],
          completed: [],
          failed: [],
        },
      });

      const result = await service.getAllQueuesStats();

      expect(result).toEqual({
        queues: expect.any(Array),
        summary: {
          totalQueues: 4, // Number of queues in QUEUE_NAMES
          totalJobs: 20, // 4 queues * 5 jobs each
          totalWaiting: 4,
          totalActive: 4,
          totalCompleted: 8,
          totalFailed: 4,
        },
      });

      expect(service.getQueueStats).toHaveBeenCalledTimes(4);
    });
  });

  describe('removeJob', () => {
    it('should remove job successfully', async () => {
      importExportQueue.getJob.mockResolvedValue(mockJob);

      await service.removeJob(QUEUE_NAMES.IMPORT_EXPORT, '12345');

      expect(importExportQueue.getJob).toHaveBeenCalledWith('12345');
      expect(mockJob.remove).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'Removed job 12345 from queue import-export',
        'QueueService'
      );
    });

    it('should handle job not found', async () => {
      importExportQueue.getJob.mockResolvedValue(null);

      await service.removeJob(QUEUE_NAMES.IMPORT_EXPORT, '12345');

      expect(mockJob.remove).not.toHaveBeenCalled();
    });
  });

  describe('retryJob', () => {
    it('should retry job successfully', async () => {
      importExportQueue.getJob.mockResolvedValue(mockJob);

      await service.retryJob(QUEUE_NAMES.IMPORT_EXPORT, '12345');

      expect(importExportQueue.getJob).toHaveBeenCalledWith('12345');
      expect(mockJob.retry).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'Retried job 12345 in queue import-export',
        'QueueService'
      );
    });
  });

  describe('cleanQueue', () => {
    it('should clean queue successfully', async () => {
      await service.cleanQueue(QUEUE_NAMES.IMPORT_EXPORT, 3600000, 100);

      expect(importExportQueue.clean).toHaveBeenCalledWith(3600000, 'completed', 100);
      expect(importExportQueue.clean).toHaveBeenCalledWith(3600000, 'failed', 100);
      expect(logger.info).toHaveBeenCalledWith(
        'Cleaned queue import-export (grace: 3600000ms, limit: 100)',
        'QueueService'
      );
    });
  });

  describe('pauseQueue', () => {
    it('should pause queue successfully', async () => {
      await service.pauseQueue(QUEUE_NAMES.IMPORT_EXPORT);

      expect(importExportQueue.pause).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Paused queue import-export', 'QueueService');
    });
  });

  describe('resumeQueue', () => {
    it('should resume queue successfully', async () => {
      await service.resumeQueue(QUEUE_NAMES.IMPORT_EXPORT);

      expect(importExportQueue.resume).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Resumed queue import-export', 'QueueService');
    });
  });

  describe('getQueueByName', () => {
    it('should return correct queue for each name', () => {
      expect(service['getQueueByName'](QUEUE_NAMES.IMPORT_EXPORT)).toBe(importExportQueue);
      expect(service['getQueueByName'](QUEUE_NAMES.FILE_PROCESSING)).toBe(fileProcessingQueue);
      expect(service['getQueueByName'](QUEUE_NAMES.EMAIL_NOTIFICATIONS)).toBe(emailQueue);
      expect(service['getQueueByName'](QUEUE_NAMES.DATA_ANALYTICS)).toBe(analyticsQueue);
    });

    it('should throw error for unknown queue name', () => {
      expect(() => service['getQueueByName']('unknown-queue')).toThrow('Unknown queue name: unknown-queue');
    });
  });

  describe('formatJobInfo', () => {
    it('should format job info correctly', () => {
      const result = service['formatJobInfo'](mockJob);

      expect(result).toEqual({
        id: mockJob.id,
        name: mockJob.name,
        data: mockJob.data,
        opts: mockJob.opts,
        progress: 50,
        delay: (mockJob as any).delay || 0,
        timestamp: mockJob.timestamp,
        attemptsMade: mockJob.attemptsMade,
        failedReason: mockJob.failedReason,
        stacktrace: mockJob.stacktrace,
        returnvalue: mockJob.returnvalue,
        finishedOn: mockJob.finishedOn,
        processedOn: mockJob.processedOn,
      });
    });
  });
});
