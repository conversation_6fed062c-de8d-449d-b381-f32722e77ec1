import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerModule } from './common/logger/logger.module';
import { RedisModule } from './common/redis/redis.module';
import { QueueModule } from './common/queue/queue.module';
import configuration from './config/configuration';

// Import all modules
import { UsersModule } from './modules/users/users.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { TeamsModule } from './modules/teams/teams.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { RolesModule } from './modules/roles/roles.module';
import { UserMenuAssignmentsModule } from './modules/user-menu-assignments/user-menu-assignments.module';
import { AuthModule } from './modules/auth/auth.module';
import { UploadModule } from './modules/upload/upload.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { DataManagementModule } from './modules/data-management/data-management.module';
import { QueueMonitorModule } from './modules/queue-monitor/queue-monitor.module';
import { ClientsModule } from './modules/clients/clients.module';
import { BctvModule } from './modules/bctv/bctv.module';
import { FileDownloadModule } from './modules/file-download/file-download.module';
import { ProposalsModule } from './modules/proposals/proposals.module';
import { BrandsModule } from './modules/brands/brands.module';
import { AgenciesModule } from './modules/agencies/agencies.module';
import { MembersModule } from './modules/members/members.module';
import { MemberTransactionsModule } from './modules/member-transactions/member-transactions.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('database.synchronize'),
        logging: configService.get('database.logging'),
        maxQueryExecutionTime: 1000,
        extra: {},
      }),
      inject: [ConfigService],
    }),
    LoggerModule,
    RedisModule,
    QueueModule,
    // RBAC Modules
    UsersModule,
    DepartmentsModule,
    TeamsModule,
    PermissionsModule,
    RolesModule,
    UserMenuAssignmentsModule,
    AuthModule,
    UploadModule,
    DashboardModule,
    DataManagementModule,
    QueueMonitorModule,
    ClientsModule,
    BctvModule,
    FileDownloadModule,
    ProposalsModule,
    BrandsModule,
    AgenciesModule,
    MembersModule,
    MemberTransactionsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
