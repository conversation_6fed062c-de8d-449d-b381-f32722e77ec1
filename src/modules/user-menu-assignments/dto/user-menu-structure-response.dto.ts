import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PermissionStructureDto {
  @ApiProperty({
    description: 'Permission ID',
    example: '10e23b0a-4e9f-4350-833b-218d04799271',
  })
  id: string;

  @ApiProperty({
    description: 'Permission name',
    example: '',
  })
  name: string;

  @ApiProperty({
    description: 'Permission action',
    example: 'view',
  })
  action: string;

  @ApiProperty({
    description: 'Permission description',
    example: 'Xem',
  })
  description: string;
}

export class SubMenuStructureDto {
  @ApiProperty({
    description: 'SubMenu ID',
    example: 'f64f580b-340c-428a-b155-ff6a3241f4c2',
  })
  id: string;

  @ApiProperty({
    description: 'SubMenu title',
    example: 'Biểu đồ thể hiện tăng trưởng của từng bộ phận theo mốc thời gian được chọn',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'SubMenu description',
    example: null,
  })
  description?: string;

  @ApiProperty({
    description: 'SubMenu link',
    example: '/bieu-do-the-hien-tang-truong-cua-tung-bo-phan-theo-moc-thoi-gian-duoc-chon',
  })
  link: string;

  @ApiProperty({
    description: 'SubMenu permissions',
    type: [PermissionStructureDto],
  })
  permissions: PermissionStructureDto[];
}

export class MenuStructureDto {
  @ApiProperty({
    description: 'Menu ID',
    example: '6892efc8-0764-4608-946b-60d56271837b',
  })
  id: string;

  @ApiProperty({
    description: 'Menu title',
    example: 'Dashboard',
  })
  title: string;

  @ApiProperty({
    description: 'Menu description',
    example: 'Menu Dashboard',
  })
  description: string;

  @ApiProperty({
    description: 'Menu permissions',
    type: [PermissionStructureDto],
  })
  permissions: PermissionStructureDto[];

  @ApiProperty({
    description: 'Menu submenus',
    type: [SubMenuStructureDto],
  })
  submenus: SubMenuStructureDto[];
}

export class DepartmentStructureDto {
  @ApiProperty({
    description: 'Department ID',
    example: 'department-uuid-here',
  })
  id: string;

  @ApiProperty({
    description: 'Department name',
    example: 'Department Name',
  })
  name: string;
}

export class RoleStructureDto {
  @ApiProperty({
    description: 'Role ID',
    example: 'role-uuid-here',
  })
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'Role Name',
  })
  name: string;
}

export class TeamStructureDto {
  @ApiProperty({
    description: 'Team ID',
    example: 'team-uuid-here',
  })
  id: string;

  @ApiProperty({
    description: 'Team name',
    example: 'Team Name',
  })
  name: string;
}

export class UserMenuStructureResponseDto {
  @ApiPropertyOptional({
    description: 'User department information',
    type: DepartmentStructureDto,
  })
  department?: DepartmentStructureDto;

  @ApiPropertyOptional({
    description: 'User role information',
    type: RoleStructureDto,
  })
  role?: RoleStructureDto;

  @ApiPropertyOptional({
    description: 'User team information',
    type: TeamStructureDto,
  })
  team?: TeamStructureDto;

  @ApiProperty({
    description: 'User menu assignments with permissions',
    type: [MenuStructureDto],
  })
  menus: MenuStructureDto[];
}
