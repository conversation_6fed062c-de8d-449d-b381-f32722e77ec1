import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MemberTransaction, TransactionStatus } from '@/entities/member-transaction.entity';
import { Member } from '@/entities/member.entity';
import { CreateMemberTransactionDto } from './dto/create-member-transaction.dto';
import { UpdateMemberTransactionDto } from './dto/update-member-transaction.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class MemberTransactionsService {
  constructor(
    @InjectRepository(MemberTransaction)
    private readonly transactionRepository: Repository<MemberTransaction>,
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createTransactionDto: CreateMemberTransactionDto, createdBy?: string): Promise<MemberTransaction> {
    this.logger.info(
      `Creating new transaction for member: ${createTransactionDto.memberId}`,
      'MemberTransactionsService',
      { memberId: createTransactionDto.memberId, type: createTransactionDto.type, amount: createTransactionDto.amount, createdBy }
    );

    // Verify member exists
    const member = await this.memberRepository.findOne({
      where: { id: createTransactionDto.memberId },
    });
    if (!member) {
      this.logger.warn(
        `Transaction creation failed - member not found: ${createTransactionDto.memberId}`,
        'MemberTransactionsService'
      );
      throw new NotFoundException('Member not found');
    }

    try {
      const transaction = this.transactionRepository.create({
        ...createTransactionDto,
        fee: createTransactionDto.fee || 0,
        status: createTransactionDto.status || TransactionStatus.PENDING,
        createdBy,
      });

      const savedTransaction = await this.transactionRepository.save(transaction);

      this.logger.info(
        `Transaction created successfully: ${savedTransaction.id}`,
        'MemberTransactionsService',
        { transactionId: savedTransaction.id, memberId: createTransactionDto.memberId }
      );

      return savedTransaction;
    } catch (error) {
      this.logger.error(
        `Failed to create transaction for member: ${createTransactionDto.memberId}`,
        error,
        'MemberTransactionsService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<MemberTransaction>> {
    this.logger.info('Fetching all transactions', 'MemberTransactionsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
        .leftJoinAndSelect('transaction.member', 'member');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`transaction.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('transaction.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [transactions, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${transactions.length} transactions`,
        'MemberTransactionsService',
        { total, page, limit }
      );

      return createPaginatedResult(transactions, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch transactions', error, 'MemberTransactionsService');
      throw error;
    }
  }

  async findOne(id: string): Promise<MemberTransaction> {
    this.logger.info(`Fetching transaction by ID: ${id}`, 'MemberTransactionsService');

    try {
      const transaction = await this.transactionRepository.findOne({
        where: { id },
        relations: ['member'],
      });

      if (!transaction) {
        this.logger.warn(`Transaction not found: ${id}`, 'MemberTransactionsService');
        throw new NotFoundException('Transaction not found');
      }

      this.logger.info(`Transaction found: ${transaction.id}`, 'MemberTransactionsService', { transactionId: id });
      return transaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async update(id: string, updateTransactionDto: UpdateMemberTransactionDto, updatedBy?: string): Promise<MemberTransaction> {
    this.logger.info(`Updating transaction: ${id}`, 'MemberTransactionsService', { updateTransactionDto, updatedBy });

    try {
      const transaction = await this.findOne(id);

      // Update transaction
      Object.assign(transaction, updateTransactionDto, { updatedBy });
      
      const updatedTransaction = await this.transactionRepository.save(transaction);

      this.logger.info(
        `Transaction updated successfully: ${updatedTransaction.id}`,
        'MemberTransactionsService',
        { transactionId: id }
      );

      return updatedTransaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to update transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting transaction: ${id}`, 'MemberTransactionsService', { deletedBy });

    try {
      const transaction = await this.findOne(id);

      // Soft delete
      transaction.deletedBy = deletedBy;
      await this.transactionRepository.softDelete(id);

      this.logger.info(`Transaction deleted successfully: ${transaction.id}`, 'MemberTransactionsService', { transactionId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async processTransaction(id: string, updatedBy?: string): Promise<MemberTransaction> {
    this.logger.info(`Processing transaction: ${id}`, 'MemberTransactionsService', { updatedBy });

    try {
      const transaction = await this.findOne(id);
      
      transaction.status = TransactionStatus.COMPLETED;
      transaction.processedAt = new Date();
      transaction.updatedBy = updatedBy;

      const processedTransaction = await this.transactionRepository.save(transaction);

      this.logger.info(
        `Transaction processed successfully: ${processedTransaction.id}`,
        'MemberTransactionsService',
        { transactionId: id }
      );

      return processedTransaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to process transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async findByMember(memberId: string, paginationDto: PaginationDto): Promise<PaginatedResult<MemberTransaction>> {
    this.logger.info(`Fetching transactions for member: ${memberId}`, 'MemberTransactionsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
        .leftJoinAndSelect('transaction.member', 'member')
        .where('transaction.memberId = :memberId', { memberId });

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`transaction.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('transaction.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [transactions, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${transactions.length} transactions for member: ${memberId}`,
        'MemberTransactionsService',
        { total, page, limit, memberId }
      );

      return createPaginatedResult(transactions, total, page, limit);
    } catch (error) {
      this.logger.error(`Failed to fetch transactions for member: ${memberId}`, error, 'MemberTransactionsService');
      throw error;
    }
  }
}
