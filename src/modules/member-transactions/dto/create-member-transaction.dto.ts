import { IsS<PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsPos<PERSON>, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TransactionType, TransactionStatus } from '@/entities/member-transaction.entity';

export class CreateMemberTransactionDto {
  @ApiProperty({
    description: 'Member ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  memberId: string;

  @ApiProperty({
    description: 'Type of transaction',
    enum: TransactionType,
    example: TransactionType.DEPOSIT,
  })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({
    description: 'Transaction amount',
    example: 1000.50,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  amount: number;

  @ApiPropertyOptional({
    description: 'Transaction fee',
    example: 10.00,
    default: 0,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  fee?: number;

  @ApiPropertyOptional({
    description: 'Transaction status',
    enum: TransactionStatus,
    example: TransactionStatus.PENDING,
    default: TransactionStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(TransactionStatus)
  status?: TransactionStatus;

  @ApiPropertyOptional({
    description: 'Transaction description',
    example: 'Monthly deposit',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Reference number',
    example: 'REF123456789',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  referenceNumber?: string;

  @ApiPropertyOptional({
    description: 'Additional transaction data',
    example: { paymentMethod: 'credit_card', cardLast4: '1234' },
  })
  @IsOptional()
  transactionData?: Record<string, any>;
}
