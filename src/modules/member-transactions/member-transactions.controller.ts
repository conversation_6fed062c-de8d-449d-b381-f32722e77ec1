import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { MemberTransactionsService } from './member-transactions.service';
import { CreateMemberTransactionDto, UpdateMemberTransactionDto } from './dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';

@ApiTags('Member Transactions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('member-transactions')
export class MemberTransactionsController {
  constructor(private readonly memberTransactionsService: MemberTransactionsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new member transaction' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Transaction created successfully',
  })
  async create(
    @Body() createTransactionDto: CreateMemberTransactionDto,
    @Request() req: any,
  ) {
    return await this.memberTransactionsService.create(createTransactionDto, req.user?.username);
  }

  @Get()
  @ApiOperation({ summary: 'Get all transactions with pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transactions retrieved successfully',
  })
  async findAll(@Query() paginationDto: PaginationDto) {
    return await this.memberTransactionsService.findAll(paginationDto);
  }

  @Get('member/:memberId')
  @ApiOperation({ summary: 'Get transactions by member ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member transactions retrieved successfully',
  })
  async findByMember(
    @Param('memberId', ParseUUIDPipe) memberId: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return await this.memberTransactionsService.findByMember(memberId, paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a transaction by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction retrieved successfully',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return await this.memberTransactionsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a transaction' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction updated successfully',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTransactionDto: UpdateMemberTransactionDto,
    @Request() req: any,
  ) {
    return await this.memberTransactionsService.update(id, updateTransactionDto, req.user?.username);
  }

  @Patch(':id/process')
  @ApiOperation({ summary: 'Process a transaction' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction processed successfully',
  })
  async processTransaction(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    return await this.memberTransactionsService.processTransaction(id, req.user?.username);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a transaction' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Transaction deleted successfully',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.memberTransactionsService.remove(id, req.user?.username);
  }
}
