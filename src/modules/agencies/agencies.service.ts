import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agency } from '@/entities/agency.entity';
import { CreateAgencyDto } from './dto/create-agency.dto';
import { UpdateAgencyDto } from './dto/update-agency.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class AgenciesService {
  constructor(
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createAgencyDto: CreateAgencyDto, createdBy?: string): Promise<Agency> {
    this.logger.info(
      `Creating new agency: ${createAgencyDto.name}`,
      'AgenciesService',
      { name: createAgencyDto.name, createdBy }
    );

    // Check if agency name already exists
    const existingAgency = await this.agencyRepository.findOne({
      where: { name: createAgencyDto.name },
    });
    if (existingAgency) {
      this.logger.warn(
        `Agency creation failed - name already exists: ${createAgencyDto.name}`,
        'AgenciesService'
      );
      throw new ConflictException('Agency name already exists');
    }

    try {
      const agency = this.agencyRepository.create({
        ...createAgencyDto,
        createdBy,
      });

      const savedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency created successfully: ${savedAgency.name}`,
        'AgenciesService',
        { agencyId: savedAgency.id, name: savedAgency.name }
      );

      return savedAgency;
    } catch (error) {
      this.logger.error(
        `Failed to create agency: ${createAgencyDto.name}`,
        error,
        'AgenciesService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Agency>> {
    this.logger.info('Fetching all agencies', 'AgenciesService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.agencyRepository.createQueryBuilder('agency');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`agency.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('agency.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [agencies, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${agencies.length} agencies`,
        'AgenciesService',
        { total, page, limit }
      );

      return createPaginatedResult(agencies, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch agencies', error, 'AgenciesService');
      throw error;
    }
  }

  async findOne(id: string): Promise<Agency> {
    this.logger.info(`Fetching agency by ID: ${id}`, 'AgenciesService');

    try {
      const agency = await this.agencyRepository.findOne({
        where: { id },
        relations: ['members'],
      });

      if (!agency) {
        this.logger.warn(`Agency not found: ${id}`, 'AgenciesService');
        throw new NotFoundException('Agency not found');
      }

      this.logger.info(`Agency found: ${agency.name}`, 'AgenciesService', { agencyId: id });
      return agency;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async update(id: string, updateAgencyDto: UpdateAgencyDto, updatedBy?: string): Promise<Agency> {
    this.logger.info(`Updating agency: ${id}`, 'AgenciesService', { updateAgencyDto, updatedBy });

    try {
      const agency = await this.findOne(id);

      // Check if new name conflicts with existing agency (if name is being updated)
      if (updateAgencyDto.name && updateAgencyDto.name !== agency.name) {
        const existingAgency = await this.agencyRepository.findOne({
          where: { name: updateAgencyDto.name },
        });
        if (existingAgency) {
          this.logger.warn(
            `Agency update failed - name already exists: ${updateAgencyDto.name}`,
            'AgenciesService'
          );
          throw new ConflictException('Agency name already exists');
        }
      }

      // Update agency
      Object.assign(agency, updateAgencyDto, { updatedBy });
      const updatedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency updated successfully: ${updatedAgency.name}`,
        'AgenciesService',
        { agencyId: id }
      );

      return updatedAgency;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to update agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting agency: ${id}`, 'AgenciesService', { deletedBy });

    try {
      const agency = await this.findOne(id);

      // Soft delete
      agency.deletedBy = deletedBy;
      await this.agencyRepository.softDelete(id);

      this.logger.info(`Agency deleted successfully: ${agency.name}`, 'AgenciesService', { agencyId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async toggleActive(id: string, updatedBy?: string): Promise<Agency> {
    this.logger.info(`Toggling agency active status: ${id}`, 'AgenciesService', { updatedBy });

    try {
      const agency = await this.findOne(id);
      agency.active = !agency.active;
      agency.updatedBy = updatedBy;

      const updatedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency active status toggled: ${updatedAgency.name} - ${updatedAgency.active}`,
        'AgenciesService',
        { agencyId: id, active: updatedAgency.active }
      );

      return updatedAgency;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to toggle agency active status: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }
}
