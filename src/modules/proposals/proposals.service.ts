import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { InvoiceProposals, ProposalStatus } from '@/entities/invoice-proposals.entity';
import { User } from '@/entities/user.entity';
import { Department } from '@/entities/department.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { CreateProposalDto, UpdateProposalDto, ProposalQueryDto } from './dto';
import { PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';

@Injectable()
export class ProposalsService {
  constructor(
    @InjectRepository(InvoiceProposals)
    private readonly proposalsRepository: Repository<InvoiceProposals>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Create new proposal
   */
  async create(createProposalDto: CreateProposalDto, createdBy?: string): Promise<InvoiceProposals> {
    try {
      // Validate proposer exists
      const proposer = await this.userRepository.findOne({
        where: { id: createProposalDto.proposerId }
      });
      if (!proposer) {
        throw new BadRequestException(`Proposer with ID ${createProposalDto.proposerId} not found`);
      }

      // Validate department if provided
      if (createProposalDto.departmentId) {
        const department = await this.departmentRepository.findOne({
          where: { id: createProposalDto.departmentId }
        });
        if (!department) {
          throw new BadRequestException(`Department with ID ${createProposalDto.departmentId} not found`);
        }
      }

      // Calculate total amount if not provided
      if (!createProposalDto.totalAmount) {
        createProposalDto.totalAmount = (createProposalDto.unitPrice * createProposalDto.quantity) + createProposalDto.fee;
      }

      const proposal = this.proposalsRepository.create({
        ...createProposalDto,
        createdBy,
      });

      const savedProposal = await this.proposalsRepository.save(proposal);

      this.logger.log(
        `Created proposal: ${savedProposal.resourceName} by ${proposer.fullName}`,
        'ProposalsService'
      );

      return savedProposal;
    } catch (error) {
      this.logger.error(
        `Failed to create proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Get all proposals with pagination and filtering
   */
  async findAll(queryDto: ProposalQueryDto): Promise<PaginatedResult<InvoiceProposals>> {
    try {
      const {
        resourceName,
        sponsor,
        status,
        proposerId,
        departmentId,
        category,
        priority,
        proposalDateFrom,
        proposalDateTo,
        totalAmountFrom,
        totalAmountTo,
        overdue,
        page = 1,
        limit = 10
      } = queryDto;

      const queryBuilder = this.proposalsRepository
        .createQueryBuilder('proposal')
        .leftJoinAndSelect('proposal.proposer', 'proposer')
        .leftJoinAndSelect('proposal.reviewer', 'reviewer')
        .leftJoinAndSelect('proposal.department', 'department');

      // Apply filters
      if (resourceName) {
        queryBuilder.andWhere('proposal.resourceName LIKE :resourceName', {
          resourceName: `%${resourceName}%`
        });
      }

      if (sponsor) {
        queryBuilder.andWhere('proposal.sponsor LIKE :sponsor', {
          sponsor: `%${sponsor}%`
        });
      }

      if (status) {
        queryBuilder.andWhere('proposal.status = :status', { status });
      }

      if (proposerId) {
        queryBuilder.andWhere('proposal.proposerId = :proposerId', { proposerId });
      }

      if (departmentId) {
        queryBuilder.andWhere('proposal.departmentId = :departmentId', { departmentId });
      }

      if (category) {
        queryBuilder.andWhere('proposal.category LIKE :category', {
          category: `%${category}%`
        });
      }

      if (priority) {
        queryBuilder.andWhere('proposal.priority = :priority', { priority });
      }

      if (proposalDateFrom) {
        queryBuilder.andWhere('proposal.proposalDate >= :proposalDateFrom', { proposalDateFrom });
      }

      if (proposalDateTo) {
        queryBuilder.andWhere('proposal.proposalDate <= :proposalDateTo', { proposalDateTo });
      }

      if (totalAmountFrom) {
        queryBuilder.andWhere('proposal.totalAmount >= :totalAmountFrom', { totalAmountFrom });
      }

      if (totalAmountTo) {
        queryBuilder.andWhere('proposal.totalAmount <= :totalAmountTo', { totalAmountTo });
      }

      if (overdue) {
        queryBuilder.andWhere('proposal.expectedCompletionDate < :now', { now: new Date() })
                    .andWhere('proposal.status = :pendingStatus', { pendingStatus: ProposalStatus.PENDING });
      }

      // Apply pagination
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Order by creation date
      queryBuilder.orderBy('proposal.createdAt', 'DESC');

      const [data, total] = await queryBuilder.getManyAndCount();

      return createPaginatedResult(data, total, page, limit);
    } catch (error) {
      this.logger.error(
        `Failed to fetch proposals: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Get proposal by ID
   */
  async findOne(id: string): Promise<InvoiceProposals> {
    try {
      const proposal = await this.proposalsRepository.findOne({
        where: { id },
        relations: ['proposer', 'reviewer', 'department']
      });

      if (!proposal) {
        throw new NotFoundException(`Proposal with ID ${id} not found`);
      }

      return proposal;
    } catch (error) {
      this.logger.error(
        `Failed to fetch proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Update proposal
   */
  async update(id: string, updateProposalDto: UpdateProposalDto, updatedBy?: string): Promise<InvoiceProposals> {
    try {
      const proposal = await this.findOne(id);

      // Validate proposer if being updated
      if (updateProposalDto.proposerId && updateProposalDto.proposerId !== proposal.proposerId) {
        const proposer = await this.userRepository.findOne({
          where: { id: updateProposalDto.proposerId }
        });
        if (!proposer) {
          throw new BadRequestException(`Proposer with ID ${updateProposalDto.proposerId} not found`);
        }
      }

      // Validate department if being updated
      if (updateProposalDto.departmentId && updateProposalDto.departmentId !== proposal.departmentId) {
        const department = await this.departmentRepository.findOne({
          where: { id: updateProposalDto.departmentId }
        });
        if (!department) {
          throw new BadRequestException(`Department with ID ${updateProposalDto.departmentId} not found`);
        }
      }

      // Recalculate total amount if relevant fields are updated
      if (updateProposalDto.unitPrice !== undefined || 
          updateProposalDto.quantity !== undefined || 
          updateProposalDto.fee !== undefined) {
        const unitPrice = updateProposalDto.unitPrice ?? proposal.unitPrice ?? 0;
        const quantity = updateProposalDto.quantity ?? proposal.quantity;
        const fee = updateProposalDto.fee ?? proposal.fee;
        updateProposalDto.totalAmount = (unitPrice * quantity) + fee;
      }

      Object.assign(proposal, updateProposalDto, { updatedBy });
      const updatedProposal = await this.proposalsRepository.save(proposal);

      this.logger.log(
        `Updated proposal: ${updatedProposal.resourceName}`,
        'ProposalsService'
      );

      return updatedProposal;
    } catch (error) {
      this.logger.error(
        `Failed to update proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Delete proposal (soft delete)
   */
  async remove(id: string, deletedBy: string): Promise<boolean> {
    try {
      const proposal = await this.findOne(id);
      
      proposal.deletedAt = new Date();
      proposal.deletedBy = deletedBy;
      await this.proposalsRepository.save(proposal);

      this.logger.log(
        `Soft deleted proposal: ${proposal.resourceName}`,
        'ProposalsService'
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Approve proposal
   */
  async approve(id: string, reviewedBy: string): Promise<InvoiceProposals> {
    try {
      const proposal = await this.findOne(id);
      
      if (proposal.status !== ProposalStatus.PENDING && proposal.status !== ProposalStatus.IN_REVIEW) {
        throw new BadRequestException('Only pending or in-review proposals can be approved');
      }

      proposal.status = ProposalStatus.APPROVED;
      proposal.reviewedBy = reviewedBy;
      proposal.reviewedAt = new Date();
      proposal.rejectionReason = undefined;

      const approvedProposal = await this.proposalsRepository.save(proposal);

      this.logger.log(
        `Approved proposal: ${approvedProposal.resourceName} by ${reviewedBy}`,
        'ProposalsService'
      );

      return approvedProposal;
    } catch (error) {
      this.logger.error(
        `Failed to approve proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Reject proposal
   */
  async reject(id: string, reviewedBy: string, rejectionReason: string): Promise<InvoiceProposals> {
    try {
      const proposal = await this.findOne(id);
      
      if (proposal.status !== ProposalStatus.PENDING && proposal.status !== ProposalStatus.IN_REVIEW) {
        throw new BadRequestException('Only pending or in-review proposals can be rejected');
      }

      proposal.status = ProposalStatus.REJECTED;
      proposal.reviewedBy = reviewedBy;
      proposal.reviewedAt = new Date();
      proposal.rejectionReason = rejectionReason;

      const rejectedProposal = await this.proposalsRepository.save(proposal);

      this.logger.log(
        `Rejected proposal: ${rejectedProposal.resourceName} by ${reviewedBy}`,
        'ProposalsService'
      );

      return rejectedProposal;
    } catch (error) {
      this.logger.error(
        `Failed to reject proposal: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }

  /**
   * Get statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const totalProposals = await this.proposalsRepository.count();
      const pendingProposals = await this.proposalsRepository.count({ where: { status: ProposalStatus.PENDING } });
      const approvedProposals = await this.proposalsRepository.count({ where: { status: ProposalStatus.APPROVED } });
      const rejectedProposals = await this.proposalsRepository.count({ where: { status: ProposalStatus.REJECTED } });
      
      const totalAmount = await this.proposalsRepository
        .createQueryBuilder('proposal')
        .select('SUM(proposal.totalAmount)', 'total')
        .where('proposal.status = :status', { status: ProposalStatus.APPROVED })
        .getRawOne();

      const overdueProposals = await this.proposalsRepository
        .createQueryBuilder('proposal')
        .where('proposal.expectedCompletionDate < :now', { now: new Date() })
        .andWhere('proposal.status = :status', { status: ProposalStatus.PENDING })
        .getCount();

      return {
        totalProposals,
        pendingProposals,
        approvedProposals,
        rejectedProposals,
        overdueProposals,
        totalApprovedAmount: parseFloat(totalAmount.total) || 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get proposal statistics: ${error.message}`,
        error.stack,
        'ProposalsService'
      );
      throw error;
    }
  }
}
