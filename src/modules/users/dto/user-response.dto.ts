import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';


export class TeamSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class DepartmentSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class RoleSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'Full name',
    example: 'John Do<PERSON>',
  })
  @Expose()
  fullName: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiPropertyOptional({
    description: 'Phone number',
    example: '+1234567890',
  })
  @Expose()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Address',
    example: '123 Main St, Apt 4B',
  })
  @Expose()
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
  })
  @Expose()
  city?: string;

  @ApiPropertyOptional({
    description: 'State',
    example: 'NY',
  })
  @Expose()
  state?: string;

  @ApiPropertyOptional({
    description: 'ZIP code',
    example: '10001',
  })
  @Expose()
  zip?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'USA',
  })
  @Expose()
  country?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'Affiliate code for tracking employee performance',
    example: 'AFF-JOHN-2024',
  })
  @Expose()
  affiliateCode?: string;

  @ApiPropertyOptional({
    description: 'ID of the user who referred this user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  referredBy?: string;

  @ApiPropertyOptional({
    description: 'Date when the affiliate code was assigned',
    example: '2024-01-15T10:30:00Z',
  })
  @Expose()
  affiliateCodeAssignedAt?: Date;

  @ApiPropertyOptional({
    description: 'Whether the affiliate code is active',
    example: true,
  })
  @Expose()
  affiliateCodeActive?: boolean;

  @ApiPropertyOptional({
    description: 'Two-factor authentication enabled',
    example: false,
  })
  @Expose()
  twoFactorEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Whitelist IP addresses',
    example: ['***********', '********'],
    type: [String],
  })
  @Expose()
  whitelistIPs?: string[];

  // Exclude password and sensitive data from response
  @Exclude()
  password: string;

  @Exclude()
  twoFactorSecret?: string;

  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Team',
    type: TeamSummaryDto
  })
  @Expose()
  @Type(() => TeamSummaryDto)
  team?: TeamSummaryDto;

  @ApiPropertyOptional({
    description: 'Department name',
    type: DepartmentSummaryDto
  })
  @Expose()
  @Type(() => DepartmentSummaryDto)
  department?: DepartmentSummaryDto;

  @ApiPropertyOptional({
    description: 'Role name',
    type: RoleSummaryDto
  })
  @Expose()
  @Type(() => RoleSummaryDto)
  role?: RoleSummaryDto;
}

