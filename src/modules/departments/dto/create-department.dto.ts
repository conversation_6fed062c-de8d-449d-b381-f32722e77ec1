import { IsString, MaxLength, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDepartmentDto {
  @ApiProperty({
    description: 'Name of the department',
    example: 'Human Resources',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Agency associated with the department',
    example: 'Marketing Agency ABC',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  agency?: string;
}
