import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Member } from '@/entities/member.entity';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class MembersService {
  constructor(
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createMemberDto: CreateMemberDto, createdBy?: string): Promise<Member> {
    this.logger.info(
      `Creating new member: ${createMemberDto.friendId}`,
      'MembersService',
      { friendId: createMemberDto.friendId, createdBy }
    );

    // Check if friend ID already exists
    const existingMember = await this.memberRepository.findOne({
      where: { friendId: createMemberDto.friendId },
    });
    if (existingMember) {
      this.logger.warn(
        `Member creation failed - friend ID already exists: ${createMemberDto.friendId}`,
        'MembersService'
      );
      throw new ConflictException('Friend ID already exists');
    }

    try {
      const member = this.memberRepository.create({
        ...createMemberDto,
        joinDate: createMemberDto.joinDate ? new Date(createMemberDto.joinDate) : new Date(),
        createdBy,
      });

      const savedMember = await this.memberRepository.save(member);

      this.logger.info(
        `Member created successfully: ${savedMember.friendId}`,
        'MembersService',
        { memberId: savedMember.id, friendId: savedMember.friendId }
      );

      return savedMember;
    } catch (error) {
      this.logger.error(
        `Failed to create member: ${createMemberDto.friendId}`,
        error,
        'MembersService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Member>> {
    this.logger.info('Fetching all members', 'MembersService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.memberRepository.createQueryBuilder('member')
        .leftJoinAndSelect('member.agency', 'agency');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`member.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('member.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [members, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${members.length} members`,
        'MembersService',
        { total, page, limit }
      );

      return createPaginatedResult(members, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch members', error, 'MembersService');
      throw error;
    }
  }

  async findOne(id: string): Promise<Member> {
    this.logger.info(`Fetching member by ID: ${id}`, 'MembersService');

    try {
      const member = await this.memberRepository.findOne({
        where: { id },
        relations: ['agency', 'transactions'],
      });

      if (!member) {
        this.logger.warn(`Member not found: ${id}`, 'MembersService');
        throw new NotFoundException('Member not found');
      }

      this.logger.info(`Member found: ${member.friendId}`, 'MembersService', { memberId: id });
      return member;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch member: ${id}`, error, 'MembersService');
      throw error;
    }
  }

  async update(id: string, updateMemberDto: UpdateMemberDto, updatedBy?: string): Promise<Member> {
    this.logger.info(`Updating member: ${id}`, 'MembersService', { updateMemberDto, updatedBy });

    try {
      const member = await this.findOne(id);

      // Check if new friend ID conflicts with existing member (if friendId is being updated)
      if (updateMemberDto.friendId && updateMemberDto.friendId !== member.friendId) {
        const existingMember = await this.memberRepository.findOne({
          where: { friendId: updateMemberDto.friendId },
        });
        if (existingMember) {
          this.logger.warn(
            `Member update failed - friend ID already exists: ${updateMemberDto.friendId}`,
            'MembersService'
          );
          throw new ConflictException('Friend ID already exists');
        }
      }

      // Update member
      Object.assign(member, updateMemberDto, { updatedBy });
      if (updateMemberDto.joinDate) {
        member.joinDate = new Date(updateMemberDto.joinDate);
      }
      
      const updatedMember = await this.memberRepository.save(member);

      this.logger.info(
        `Member updated successfully: ${updatedMember.friendId}`,
        'MembersService',
        { memberId: id }
      );

      return updatedMember;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to update member: ${id}`, error, 'MembersService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting member: ${id}`, 'MembersService', { deletedBy });

    try {
      const member = await this.findOne(id);

      // Soft delete
      member.deletedBy = deletedBy;
      await this.memberRepository.softDelete(id);

      this.logger.info(`Member deleted successfully: ${member.friendId}`, 'MembersService', { memberId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete member: ${id}`, error, 'MembersService');
      throw error;
    }
  }
}
