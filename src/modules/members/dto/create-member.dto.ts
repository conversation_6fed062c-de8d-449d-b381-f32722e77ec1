import { IsString, IsOptional, IsBoolean, IsEmail, IsDateString, Max<PERSON>ength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateMemberDto {
  @ApiProperty({
    description: 'Friend ID of the member',
    example: 'FRIEND001',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100)
  friendId: string;

  @ApiProperty({
    description: 'Full name of the member',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  fullname: string;

  @ApiPropertyOptional({
    description: 'Member level',
    example: 'Gold',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  level?: string;

  @ApiPropertyOptional({
    description: 'Email address',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({
    description: 'Phone number',
    example: '+1234567890',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiPropertyOptional({
    description: 'Address',
    example: '123 Main St, City, State 12345',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Join date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  joinDate?: string;

  @ApiPropertyOptional({
    description: 'Agency ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  agencyId?: string;

  @ApiPropertyOptional({
    description: 'Whether the member is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;
}
