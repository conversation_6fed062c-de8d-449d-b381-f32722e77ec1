import { Processor, Process } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bull';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { User, MemberStatistics } from '@/entities';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { QUEUE_NAMES, JOB_TYPES } from '@/common/queue/queue.constants';

export interface ImportJobData {
  filePath: string;
  entityType: string;
  userId: number;
  options?: {
    skipFirstRow?: boolean;
    delimiter?: string;
    encoding?: string;
  };
}

export interface ExportJobData {
  entityType: string;
  userId: number;
  filters?: any;
  outputPath?: string;
  format?: 'csv' | 'json' | 'xlsx';
  options?: {
    includeHeaders?: boolean;
    delimiter?: string;
    encoding?: string;
    sheetName?: string;
  };
}

@Injectable()
@Processor(QUEUE_NAMES.IMPORT_EXPORT)
export class ImportExportProcessor {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(MemberStatistics) private membersRepository: Repository<MemberStatistics>,
    private readonly logger: CustomLoggerService,
  ) {}

  @Process(JOB_TYPES.IMPORT_USERS)
  async importUsers(job: Job<ImportJobData>) {
    const { filePath, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting user import from ${filePath}`, 'ImportExportProcessor');

      const users = await this.parseFile(filePath, options);
      const totalUsers = users.length;
      let processedUsers = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const userData of users) {
        try {
          // Validate required fields
          if (!userData.username || !userData.email) {
            throw new Error('Username and email are required');
          }

          // Check if user already exists
          const existingUser = await this.userRepository.findOne({
            where: [
              { username: userData.username },
              { email: userData.email }
            ]
          });

          if (existingUser) {
            throw new Error(`User with username ${userData.username} or email ${userData.email} already exists`);
          }

          // Create new user
          const user = this.userRepository.create({
            username: userData.username,
            email: userData.email,
            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || userData.username,
            phone: userData.phone || null,
            password: 'temp_password', // This should be handled properly in real implementation
            // Add other fields as needed
          });

          await this.userRepository.save(user);
          successCount++;
        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedUsers + 1}: ${error.message}`);
          this.logger.warn(`Failed to import user at row ${processedUsers + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedUsers++;
        await job.progress(Math.round((processedUsers / totalUsers) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedUsers,
        successCount,
        errorCount,
        errors: errors.slice(0, 100), // Limit errors to prevent memory issues
      };

      this.logger.info(
        `User import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`User import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.IMPORT_FTD)
  async importFtd(job: Job<ImportJobData>) {
    const { filePath, options = {} } = job.data;

    try {
      await job.progress(0);

      this.logger.info(`Starting FTD import from file: ${filePath}`, 'ImportExportProcessor');

      // Parse file based on extension
      const fileExtension = path.extname(filePath).toLowerCase();
      let parsedData: any[] = [];

      if (fileExtension === '.xlsx' || fileExtension === '.xls') {
        parsedData = this.parseExcelFile(filePath, options);
      } else if (fileExtension === '.csv') {
        parsedData = this.parseCSVFile(filePath, options);
      } else {
        throw new Error(`Unsupported file format: ${fileExtension}`);
      }

      await job.progress(20);

      if (parsedData.length === 0) {
        throw new Error('No data found in file');
      }

      // Convert Excel data to FTD format
      const ftdDataList = this.convertToFtdData(parsedData);

      await job.progress(40);

      // Import data in batches
      const batchSize = 100;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (let i = 0; i < ftdDataList.length; i += batchSize) {
        const batch = ftdDataList.slice(i, i + batchSize);

        for (const ftdData of batch) {
          try {
            // Check if account already exists
            const existingFtd = await this.membersRepository.findOne({
              where: { accountNumber: ftdData.accountNumber }
            });

            if (existingFtd) {
              // Update existing record
              Object.assign(existingFtd, ftdData);
              await this.membersRepository.save(existingFtd);
            } else {
              // Create new record
              const newFtd = this.membersRepository.create(ftdData);
              await this.membersRepository.save(newFtd);
            }

            successCount++;
          } catch (error) {
            errorCount++;
            errors.push(`Account ${ftdData.accountNumber}: ${error.message}`);
          }
        }

        const progress = Math.min(40 + ((i + batchSize) / ftdDataList.length) * 50, 90);
        await job.progress(progress);
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      await job.progress(100);

      const result = {
        totalProcessed: ftdDataList.length,
        successCount,
        errorCount,
        errors: errors.slice(0, 10), // Limit errors to first 10
      };

      this.logger.info(
        `FTD import completed: ${successCount} success, ${errorCount} failed`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`FTD import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_FTD)
  async exportFtd(job: Job<ExportJobData>) {
    const { filters = {}, outputPath, format = 'xlsx', options = {} } = job.data;

    try {
      await job.progress(0);

      this.logger.info(`Starting FTD export in ${format} format`, 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.membersRepository.createQueryBuilder('ftd');

      // Apply filters
      if (filters.accountNumber) {
        queryBuilder.andWhere('ftd.accountNumber LIKE :accountNumber', {
          accountNumber: `%${filters.accountNumber}%`
        });
      }

      if (filters.realName) {
        queryBuilder.andWhere('ftd.realName LIKE :realName', {
          realName: `%${filters.realName}%`
        });
      }

      if (filters.directFranchise) {
        queryBuilder.andWhere('ftd.directFranchise LIKE :directFranchise', {
          directFranchise: `%${filters.directFranchise}%`
        });
      }

      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('ftd.isActive = :isActive', { isActive: filters.isActive });
      }

      if (filters.joinTimeFrom) {
        queryBuilder.andWhere('ftd.joinTime >= :joinTimeFrom', { joinTimeFrom: filters.joinTimeFrom });
      }

      if (filters.joinTimeTo) {
        queryBuilder.andWhere('ftd.joinTime <= :joinTimeTo', { joinTimeTo: filters.joinTimeTo });
      }

      queryBuilder.orderBy('ftd.createdAt', 'DESC');

      await job.progress(20);

      queryBuilder.select([
        'ftd.accountNumber AS "Số tài khoản"',
        'ftd.joinTime AS "Ngày tham gia"',
        'ftd.realName AS "Họ tên"',
        'ftd.directFranchise AS "Đại lý trực thuộc"',
        'ftd.depositCount AS "Số lần nạp"',
        'ftd.depositAmount AS "Tổng tiền nạp"',
        'ftd.withdrawAmount AS "Tổng tiền rút"',
        'ftd.lastLoginTime AS "Lần đăng nhập cuối"'
      ]);

      const ftdData = await queryBuilder.getRawMany();

      await job.progress(50);

      // Generate filename and path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `ftd-export-${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', filename);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      // Export data based on format
      switch (format.toLowerCase()) {
        case 'xlsx':
          await this.exportToXLSX(ftdData, filePath, 'ftd', options);
          break;
        case 'csv':
          await this.exportToCSV(ftdData, filePath, 'ftd', options);
          break;
        case 'json':
          await this.exportToJSON(ftdData, filePath);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      await job.progress(90);

      const result = {
        totalExported: ftdData.length,
        filePath,
        format,
        fileSize: fs.statSync(filePath).size,
      };

      this.logger.info(
        `FTD export completed: ${ftdData.length} records exported`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`FTD export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  /**
   * Convert parsed Excel data to FTD format
   */
  private convertToFtdData(parsedData: any[]): any[] {
    return parsedData.map((record, index) => {
      try {
        const row = Object.values({...record});
        // Skip empty rows
        if (!row || row.length === 0 || !row[0]) {
          return null;
        }

        // Convert Excel date number to JavaScript Date
        const convertExcelDate = (excelDate: any): Date | null => {
          if (!excelDate) return null;

          if (typeof excelDate === 'number') {
            // Excel date serial number (days since 1900-01-01)
            const excelEpoch = new Date(1900, 0, 1);
            const jsDate = new Date(excelEpoch.getTime() + (excelDate - 1) * 24 * 60 * 60 * 1000);
            return jsDate;
          }

          if (typeof excelDate === 'string') {
            const parsed = new Date(excelDate);
            return isNaN(parsed.getTime()) ? null : parsed;
          }

          return excelDate instanceof Date ? excelDate : null;
        };

        return {
          accountNumber: String(row[0] || '').trim(),
          joinTime: convertExcelDate(row[1]),
          realName: String(row[2] || '').trim(),
          directFranchise: String(row[3] || '').trim(),
          depositCount: parseInt(String(row[4])) || 0,
          depositAmount: parseFloat(String(row[5])) || 0,
          withdrawAmount: parseFloat(String(row[6])) || 0,
          lastLoginTime: convertExcelDate(row[7]),
          isActive: true,
        };
      } catch (error) {
        this.logger.warn(`Error converting row ${index + 1}: ${error.message}`, 'ImportExportProcessor');
        return null;
      }
    }).filter(item => item !== null && item.accountNumber);
  }

  /**
   * Get headers for different entity types
   */
  private getEntityHeaders(entityType: string): string[] {
    switch (entityType.toLowerCase()) {
      case 'user':
        return [
          'id',
          'username', 
          'email',
          'fullName',
          'phone',
          'isActive',
          'department',
          'team',
          'createdAt',
          'updatedAt'
        ];
      case 'ftd':
        return [
          'Số tài khoản',
          'Thời gian tham gia',
          'tên thật',
          'Bên nhượng quyền trực tiếp',
          'Số lượng tiền gửi *',
          'Số tiền gửi *',
          'Số tiền rút*',
          'Lần đăng nhập cuối cùng'
        ];
      case 'bctv':
        return [
          'Cây nhượng quyền',
          'Phụ huynh nhượng quyền',
          'Bên nhượng quyền',
          'Mức độ',
          'Thành viên',
          'cấp thành viên',
          'Nhãn',
          'Số lần gửi tiền',
          'Tổng số tiền gửi',
          'Số lần rút tiền',
          'Số tiền rút',
          'Số tiền cược',
          'Tổng số tiền đặt cược',
          'Tổng số tiền đặt cược hợp lệ',
          'Tổng số tiền chi trả',
          'Tổng tiền thưởng',
          'Tổng khấu trừ'
        ];
      default:
        return ['id', 'createdAt', 'updatedAt'];
    }
  }

  /**
   * Parse file (CSV, XLSX, JSON) and return array of objects
   */
  private async parseFile(filePath: string, options: any = {}): Promise<any[]> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const fileExtension = path.extname(filePath).toLowerCase();
    
    try {
      switch (fileExtension) {
        case '.xlsx':
        case '.xls':
          return this.parseExcelFile(filePath, options);
        case '.csv':
          return this.parseCSVFile(filePath, options);
        case '.json':
          return this.parseJSONFile(filePath);
        default:
          throw new Error(`Unsupported file format: ${fileExtension}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse file: ${error.message}`);
    }
  }

  /**
   * Parse Excel file using xlsx library
   */
  private parseExcelFile(filePath: string, options: any = {}): any[] {
    const { sheetName, skipFirstRow = true } = options;

    // Read the workbook
    const workbook = XLSX.readFile(filePath);

    // Get sheet name (use first sheet if not specified or invalid)
    let targetSheetName = sheetName;

    // Validate sheetName
    if (!targetSheetName ||
        targetSheetName === 'string' ||
        targetSheetName.trim() === '' ||
        !workbook.SheetNames.includes(targetSheetName)) {
      targetSheetName = workbook.SheetNames[0];
      this.logger.info(`Using default sheet: ${targetSheetName}. Available sheets: ${workbook.SheetNames.join(', ')}`, 'ImportExportProcessor');
    }

    const worksheet = workbook.Sheets[targetSheetName];

    if (!worksheet) {
      throw new Error(`Sheet "${targetSheetName}" not found in Excel file. Available sheets: ${workbook.SheetNames.join(', ')}`);
    }
    
    // Convert sheet to JSON with headers
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1, // Use array of arrays format first
      defval: '', // Default value for empty cells
    });
    
    if (jsonData.length === 0) {
      return [];
    }
    
    // Get headers from first row
    const headers = jsonData[0] as string[];
    const dataRows = skipFirstRow ? jsonData.slice(1) : jsonData.slice(0);
    
    // Convert to array of objects
    return dataRows.map((row: any[]) => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
  }

  /**
   * Parse CSV file using xlsx library
   */
  private parseCSVFile(filePath: string, options: any = {}): any[] {
    const { delimiter = ',', encoding = 'utf8', skipFirstRow = true } = options;
    
    // Read CSV file content
    const csvContent = fs.readFileSync(filePath, { encoding });
    
    // Use xlsx to parse CSV
    const workbook = XLSX.read(csvContent, {
      type: 'string',
      raw: false,
      FS: delimiter,
    });
    
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      defval: '',
    });
    
    if (jsonData.length === 0) {
      return [];
    }
    
    // Get headers from first row
    const headers = jsonData[0] as string[];
    const dataRows = skipFirstRow ? jsonData.slice(1) : jsonData.slice(0);
    
    // Convert to array of objects
    return dataRows.map((row: any[]) => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
  }

  /**
   * Parse JSON file
   */
  private parseJSONFile(filePath: string): any[] {
    try {
      const jsonContent = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(jsonContent);
      
      if (!Array.isArray(data)) {
        throw new Error('JSON file must contain an array of objects');
      }
      
      return data;
    } catch (error) {
      throw new Error(`Failed to parse JSON file: ${error.message}`);
    }
  }

  /**
   * Export data to Excel file using xlsx library
   */
  private async exportToXLSX(data: any[], filePath: string, entityType: string, options: any = {}): Promise<void> {
    const { sheetName = 'Sheet1', includeHeaders = true } = options;
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    
    // Convert data to worksheet
    let worksheet;
    if (data.length === 0 && includeHeaders) {
      // Create worksheet with just headers when data is empty
      const headers = this.getEntityHeaders(entityType);
      worksheet = XLSX.utils.aoa_to_sheet([headers]);
    } else {
      worksheet = XLSX.utils.json_to_sheet(data, {
        header: [],
        skipHeader: false
      });
    }
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // Write file
    XLSX.writeFile(workbook, filePath);
  }

  /**
   * Export data to CSV file using xlsx library
   */
  private async exportToCSV(data: any[], filePath: string, entityType: string, options: any = {}): Promise<void> {
    const { delimiter = ',', includeHeaders = true } = options;
    
    // Create worksheet from JSON data
    let worksheet;
    if (data.length === 0 && includeHeaders) {
      // Create worksheet with just headers when data is empty
      const headers = this.getEntityHeaders(entityType);
      worksheet = XLSX.utils.aoa_to_sheet([headers]);
    } else {
      worksheet = XLSX.utils.json_to_sheet(data, {
        skipHeader: !includeHeaders
      });
    }
    
    // Convert worksheet to CSV
    const csvContent = XLSX.utils.sheet_to_csv(worksheet, {
      FS: delimiter, // Field separator
      RS: '\n', // Record separator
    });
    
    // Write to file
    fs.writeFileSync(filePath, csvContent, 'utf8');
  }

  /**
   * Export data to JSON file
   */
  private async exportToJSON(data: any[], filePath: string): Promise<void> {
    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonData, 'utf8');
  }
}
