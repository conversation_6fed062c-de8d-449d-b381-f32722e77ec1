import { Test, TestingModule } from '@nestjs/testing';
import { DataManagementController } from './data-management.controller';
import { DataManagementService } from './data-management.service';
import { QueueService } from '../../common/queue/queue.service';
import { JOB_TYPES } from '../../common/queue/queue.constants';
import { EntityType, ExportFormat } from './dto/import-export.dto';

describe('DataManagementController', () => {
  let controller: DataManagementController;
  let dataManagementService: jest.Mocked<DataManagementService>;
  let queueService: jest.Mocked<QueueService>;

  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
  };

  const mockJob = {
    id: '12345',
    name: 'test-job',
    data: {},
    opts: {},
    progress: jest.fn().mockReturnValue(0),
    timestamp: Date.now(),
    attemptsMade: 0,
    failedReason: null,
    returnvalue: null,
    finishedOn: null,
  };

  beforeEach(async () => {
    const mockDataManagementService = {
      generateReport: jest.fn(),
    };

    const mockQueueService = {
      addImportExportJob: jest.fn(),
      getJob: jest.fn(),
      getAllQueuesStats: jest.fn(),
      getQueueStats: jest.fn(),
      removeJob: jest.fn(),
      retryJob: jest.fn(),
      cleanQueue: jest.fn(),
      pauseQueue: jest.fn(),
      resumeQueue: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DataManagementController],
      providers: [
        {
          provide: DataManagementService,
          useValue: mockDataManagementService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    controller = module.get<DataManagementController>(DataManagementController);
    dataManagementService = module.get(DataManagementService);
    queueService = module.get(QueueService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('importData', () => {
    const mockFile = {
      fieldname: 'file',
      originalname: 'test.csv',
      encoding: '7bit',
      mimetype: 'text/csv',
      size: 1024,
      destination: './uploads/temp',
      filename: 'test-123.csv',
      path: './uploads/temp/test-123.csv',
    };

    const importDto = {
      entityType: EntityType.USERS,
      file: mockFile,
      skipFirstRow: true,
      delimiter: ',',
      encoding: 'utf8',
    };

    it('should successfully queue import job', async () => {
      queueService.addImportExportJob.mockResolvedValue(mockJob as any);

      const result = await controller.importData(mockFile, importDto, mockUser);

      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.IMPORT_USERS,
        {
          filePath: mockFile.path,
          entityType: importDto.entityType,
          userId: mockUser.id,
          options: {
            skipFirstRow: importDto.skipFirstRow,
            delimiter: importDto.delimiter,
            encoding: importDto.encoding,
          },
        }
      );

      expect(result).toEqual({
        jobId: mockJob.id,
        entityType: importDto.entityType,
        status: 'queued',
        estimatedTime: 5, // Default for users
        message: `Import job for ${importDto.entityType} has been queued successfully`,
      });
    });

    it('should throw error when no file uploaded', async () => {
      await expect(controller.importData(null, importDto, mockUser)).rejects.toThrow('No file uploaded');
    });

    it('should throw error for unsupported entity type', async () => {
      const invalidDto = { ...importDto, entityType: 'invalid' as EntityType };
      
      await expect(controller.importData(mockFile, invalidDto, mockUser)).rejects.toThrow('Unsupported entity type: invalid');
    });

    it('should handle different entity types correctly', async () => {
      queueService.addImportExportJob.mockResolvedValue(mockJob as any);

      // Test affiliates
      const affiliateDto = { ...importDto, entityType: EntityType.AFFILIATES };
      await controller.importData(mockFile, affiliateDto, mockUser);
      
      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.IMPORT_AFFILIATES,
        expect.any(Object)
      );

      // Test bets
      const betDto = { ...importDto, entityType: EntityType.BETS };
      await controller.importData(mockFile, betDto, mockUser);
      
      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.IMPORT_BETS,
        expect.any(Object)
      );

      // Test deposits
      const depositDto = { ...importDto, entityType: EntityType.DEPOSITS };
      await controller.importData(mockFile, depositDto, mockUser);
      
      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.IMPORT_DEPOSITS,
        expect.any(Object)
      );
    });
  });

  describe('exportData', () => {
    const exportDto = {
      entityType: EntityType.USERS,
      format: ExportFormat.CSV,
      includeHeaders: true,
      delimiter: ',',
      isActive: true,
      departmentId: 1,
      teamId: 2,
      createdFrom: '2024-01-01',
      createdTo: '2024-12-31',
    };

    it('should successfully queue export job', async () => {
      queueService.addImportExportJob.mockResolvedValue(mockJob as any);

      const result = await controller.exportData(exportDto, mockUser);

      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.EXPORT_USERS,
        {
          entityType: exportDto.entityType,
          userId: mockUser.id,
          filters: {
            isActive: exportDto.isActive,
            departmentId: exportDto.departmentId,
            teamId: exportDto.teamId,
            createdFrom: exportDto.createdFrom,
            createdTo: exportDto.createdTo,
          },
          format: exportDto.format,
          options: {
            includeHeaders: exportDto.includeHeaders,
            delimiter: exportDto.delimiter,
          },
        }
      );

      expect(result).toEqual({
        jobId: mockJob.id,
        entityType: exportDto.entityType,
        format: exportDto.format,
        status: 'queued',
        estimatedTime: 3, // Default for users export
        message: `Export job for ${exportDto.entityType} has been queued successfully`,
      });
    });

    it('should handle filters correctly', async () => {
      queueService.addImportExportJob.mockResolvedValue(mockJob as any);

      // Test with minimal filters
      const minimalDto = {
        entityType: EntityType.USERS,
        format: ExportFormat.JSON,
      };

      await controller.exportData(minimalDto, mockUser);

      expect(queueService.addImportExportJob).toHaveBeenCalledWith(
        JOB_TYPES.EXPORT_USERS,
        expect.objectContaining({
          filters: {},
        })
      );
    });

    it('should throw error for unsupported entity type', async () => {
      const invalidDto = { ...exportDto, entityType: 'invalid' as EntityType };
      
      await expect(controller.exportData(invalidDto, mockUser)).rejects.toThrow('Unsupported entity type: invalid');
    });
  });

  describe('getJobStatus', () => {
    it('should return job status successfully', async () => {
      const mockJobWithState = {
        ...mockJob,
        getState: jest.fn().mockResolvedValue('active'),
      };

      queueService.getJob.mockResolvedValue(mockJobWithState as any);

      const result = await controller.getJobStatus('import-export', '12345');

      expect(queueService.getJob).toHaveBeenCalledWith('import-export', '12345');
      expect(result).toEqual({
        id: mockJob.id,
        name: mockJob.name,
        status: 'active',
        progress: 0,
        data: mockJob.data,
        attemptsMade: mockJob.attemptsMade,
        timestamp: mockJob.timestamp,
        failedReason: mockJob.failedReason,
        result: mockJob.returnvalue,
        finishedOn: mockJob.finishedOn,
      });
    });

    it('should throw error when job not found', async () => {
      queueService.getJob.mockResolvedValue(null);

      await expect(controller.getJobStatus('import-export', '12345')).rejects.toThrow('Job not found');
    });
  });

  describe('getAllQueuesStats', () => {
    it('should return all queues statistics', async () => {
      const mockStats = {
        queues: [],
        summary: {
          totalQueues: 4,
          totalJobs: 100,
          totalWaiting: 10,
          totalActive: 5,
          totalCompleted: 80,
          totalFailed: 5,
        },
      };

      queueService.getAllQueuesStats.mockResolvedValue(mockStats);

      const result = await controller.getAllQueuesStats();

      expect(queueService.getAllQueuesStats).toHaveBeenCalled();
      expect(result).toEqual(mockStats);
    });
  });

  describe('getQueueStats', () => {
    it('should return specific queue statistics', async () => {
      const mockQueueStats = {
        name: 'import-export',
        counts: {
          waiting: 5,
          active: 2,
          completed: 100,
          failed: 3,
          delayed: 0,
          paused: 0,
        },
        jobs: {
          waiting: [],
          active: [],
          completed: [],
          failed: [],
        },
      };

      queueService.getQueueStats.mockResolvedValue(mockQueueStats);

      const result = await controller.getQueueStats('import-export');

      expect(queueService.getQueueStats).toHaveBeenCalledWith('import-export');
      expect(result).toEqual(mockQueueStats);
    });
  });

  describe('removeJob', () => {
    it('should remove job successfully', async () => {
      queueService.removeJob.mockResolvedValue(undefined);

      const result = await controller.removeJob('import-export', '12345');

      expect(queueService.removeJob).toHaveBeenCalledWith('import-export', '12345');
      expect(result).toEqual({ message: 'Job removed successfully' });
    });
  });

  describe('retryJob', () => {
    it('should retry job successfully', async () => {
      queueService.retryJob.mockResolvedValue(undefined);

      const result = await controller.retryJob('import-export', '12345');

      expect(queueService.retryJob).toHaveBeenCalledWith('import-export', '12345');
      expect(result).toEqual({ message: 'Job retried successfully' });
    });
  });

  describe('cleanQueue', () => {
    it('should clean queue successfully', async () => {
      queueService.cleanQueue.mockResolvedValue(undefined);

      const result = await controller.cleanQueue('import-export', 3600000, 100);

      expect(queueService.cleanQueue).toHaveBeenCalledWith('import-export', 3600000, 100);
      expect(result).toEqual({ message: 'Queue import-export cleaned successfully' });
    });

    it('should use default values when not provided', async () => {
      queueService.cleanQueue.mockResolvedValue(undefined);

      await controller.cleanQueue('import-export');

      expect(queueService.cleanQueue).toHaveBeenCalledWith('import-export', 3600000, 100);
    });
  });

  describe('pauseQueue', () => {
    it('should pause queue successfully', async () => {
      queueService.pauseQueue.mockResolvedValue(undefined);

      const result = await controller.pauseQueue('import-export');

      expect(queueService.pauseQueue).toHaveBeenCalledWith('import-export');
      expect(result).toEqual({ message: 'Queue import-export paused successfully' });
    });
  });

  describe('resumeQueue', () => {
    it('should resume queue successfully', async () => {
      queueService.resumeQueue.mockResolvedValue(undefined);

      const result = await controller.resumeQueue('import-export');

      expect(queueService.resumeQueue).toHaveBeenCalledWith('import-export');
      expect(result).toEqual({ message: 'Queue import-export resumed successfully' });
    });
  });

  describe('getEstimatedProcessingTime', () => {
    it('should calculate correct estimated times', () => {
      // Access private method through any casting for testing
      const controllerAny = controller as any;

      expect(controllerAny.getEstimatedProcessingTime('users', 'import')).toBe(5);
      expect(controllerAny.getEstimatedProcessingTime('users', 'export')).toBe(3);
      expect(controllerAny.getEstimatedProcessingTime('affiliates', 'import')).toBe(3); // 5 * 0.5 = 2.5, ceil = 3
      expect(controllerAny.getEstimatedProcessingTime('bets', 'import')).toBe(8); // 5 * 1.5 = 7.5, ceil = 8
      expect(controllerAny.getEstimatedProcessingTime('unknown', 'import')).toBe(5); // Default multiplier 1.0
    });
  });
});
