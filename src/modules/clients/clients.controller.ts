import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ClientsService } from './clients.service';
import { CreateClientDto, UpdateClientDto, ClientQueryDto } from './dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants';
import { PermissionsType } from '@/common/constants';
import { User } from '@/entities';
import { QueueService } from '@/common/queue/queue.service';
import { JOB_TYPES } from '@/common/queue/queue.constants';
import { CustomLoggerService } from '@/common/logger/logger.service';

@ApiTags('FTD Management')
@ApiBearerAuth('JWT-auth')
@Controller('ftd')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class ClientsController {
  constructor(
    private readonly clientsService: ClientsService,
    private readonly queueService: QueueService,
    private readonly logger: CustomLoggerService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Client record' })
  @ApiResponse({ status: 201, description: 'Client record created successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  create(@Body() createClientDto: CreateClientDto) {
    return this.clientsService.create(createClientDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all Client records with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'Client records retrieved successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findAll(
    @Query() queryDto: ClientQueryDto
  ) {
    return this.clientsService.findAll(queryDto);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get Client statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  getStatistics() {
    return this.clientsService.getStatistics();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get Client record by ID' })
  @ApiResponse({ status: 200, description: 'Client record retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Client record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findOne(@Param('id') id: string) {
    return this.clientsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update Client record' })
  @ApiResponse({ status: 200, description: 'Client record updated successfully' })
  @ApiResponse({ status: 404, description: 'Client record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  update(@Param('id') id: string, @Body() updateClientDto: UpdateClientDto) {
    return this.clientsService.update(id, updateClientDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete Client record (soft delete)' })
  @ApiResponse({ status: 200, description: 'Client record deleted successfully' })
  @ApiResponse({ status: 404, description: 'Client record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  remove(@Param('id') id: string, @CurrentUser() user: any) {
    return this.clientsService.remove(id, user.fullName || user.username);
  }

  // Import/Export Operations
  @Post('import')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Import Client data from Excel file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        skipFirstRow: {
          type: 'boolean',
          default: true,
        },
        sheetName: {
          type: 'string',
          description: 'Excel sheet name (optional)',
        },
      },
    },
  })
  @ApiResponse({ status: 202, description: 'Import job queued successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          cb(null, 'client-import-' + uniqueSuffix + extname(file.originalname));
        },
      }),
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel', // .xls
          'text/csv', // .csv
          'application/csv', // .csv alternative
          'text/plain' // .csv alternative
        ];

        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
        const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));

        if (allowedMimeTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
          cb(null, true);
        } else {
          cb(new Error(`Only Excel (.xlsx, .xls) and CSV files are allowed! Received: ${file.mimetype}, Extension: ${fileExtension}`), false);
        }
      },
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB
      },
    }),
  )
  async importClientData(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @Body('skipFirstRow') skipFirstRow = true,
    @Body('sheetName') sheetName?: string,
  ) {
    if (!file) {
      throw new Error('No file uploaded');
    }

    this.logger.log(
      `Starting Client import for user ${currentUser.id}, file: ${file.filename}`,
      'ClientsController'
    );

    // Add job to queue
    const job = await this.queueService.addImportExportJob(JOB_TYPES.IMPORT_FTD, {
      filePath: file.path,
      entityType: 'clients',
      userId: currentUser.id,
      options: {
        skipFirstRow,
        sheetName: sheetName && sheetName !== 'string' && sheetName.trim() !== '' ? sheetName : undefined,
      },
    });

    return {
      jobId: job.id,
      entityType: 'clients',
      status: 'queued',
      message: 'Client import job has been queued successfully',
    };
  }

  @Post('export')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Export Client data to Excel/CSV file' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        format: {
          type: 'string',
          enum: ['xlsx', 'csv'],
          default: 'xlsx',
        },
        filters: {
          type: 'object',
          properties: {
            username: { type: 'string' },
            realName: { type: 'string' },
            directFranchise: { type: 'string' },
            isActive: { type: 'boolean' },
            joinTimeFrom: { type: 'string', format: 'date-time' },
            joinTimeTo: { type: 'string', format: 'date-time' },
          },
        },
        includeHeaders: {
          type: 'boolean',
          default: true,
        },
        sheetName: {
          type: 'string',
          default: 'Client Data',
        },
      },
    },
  })
  @ApiResponse({ status: 202, description: 'Export job queued successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  async exportClientData(
    @Body('format') format: 'xlsx' | 'csv' = 'xlsx',
    @Body('filters') filters: any = {},
    @Body('includeHeaders') includeHeaders: boolean = true,
    @Body('sheetName') sheetName: string = 'Client Data',
    @CurrentUser() currentUser: User,
  ) {
    this.logger.log(
      `Starting Client export for user ${currentUser.id}, format: ${format}`,
      'ClientsController'
    );

    // Add job to queue
    const job = await this.queueService.addImportExportJob(JOB_TYPES.EXPORT_FTD, {
      entityType: 'clients',
      userId: currentUser.id,
      filters,
      format,
      options: {
        includeHeaders,
        sheetName,
      },
    });

    return {
      jobId: job.id,
      entityType: 'clients',
      status: 'queued',
      format,
      message: 'Client export job has been queued successfully',
    };
  }
}
