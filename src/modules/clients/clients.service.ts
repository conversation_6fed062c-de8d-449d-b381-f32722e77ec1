import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, Between } from 'typeorm';
import { MemberStatistics } from '@/entities';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { CreateClientDto, UpdateClientDto, ClientQueryDto } from './dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';

@Injectable()
export class ClientsService {
  constructor(
    @InjectRepository(MemberStatistics)
    private readonly clientsRepository: Repository<MemberStatistics>,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Create new Client record
   */
  async create(createClientDto: CreateClientDto): Promise<MemberStatistics> {
    try {
      // Check if account number already exists
      const existingClient = await this.clientsRepository.findOne({
        where: { username: createClientDto.username }
      });

      if (existingClient) {
        throw new BadRequestException(`Account number ${createClientDto.username} already exists`);
      }

      if (!createClientDto.isActive) {
        createClientDto.isActive = true;
      }

      const clientData = this.clientsRepository.create(createClientDto);
      const savedClient = await this.clientsRepository.save(clientData);

      this.logger.log(
        `Created Client record for account: ${savedClient.username}`,
        'ClientsService'
      );

      return savedClient;
    } catch (error) {
      this.logger.error(
        `Failed to create Client record: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Get all Client records with pagination and filtering
   */
  async findAll(queryDto: ClientQueryDto): Promise<PaginatedResult<MemberStatistics>> {
    try {
      const {
        username,
        realName,
        directFranchise,
        joinTimeFrom,
        joinTimeTo,
        lastLoginFrom,
        lastLoginTo,
        page = 1,
        limit = 10
      } = queryDto;

      const queryBuilder = this.clientsRepository.createQueryBuilder('client');

      // Apply filters
      if (username) {
        queryBuilder.andWhere('client.username LIKE :username', {
          username: `%${username}%`
        });
      }

      if (realName) {
        queryBuilder.andWhere('client.realName LIKE :realName', {
          realName: `%${realName}%`
        });
      }

      if (directFranchise) {
        queryBuilder.andWhere('client.directFranchise LIKE :directFranchise', {
          directFranchise: `%${directFranchise}%`
        });
      }

      if (joinTimeFrom) {
        queryBuilder.andWhere('client.joinTime >= :joinTimeFrom', { joinTimeFrom });
      }

      if (joinTimeTo) {
        queryBuilder.andWhere('client.joinTime <= :joinTimeTo', { joinTimeTo });
      }

      if (lastLoginFrom) {
        queryBuilder.andWhere('client.lastLoginTime >= :lastLoginFrom', { lastLoginFrom });
      }

      if (lastLoginTo) {
        queryBuilder.andWhere('client.lastLoginTime <= :lastLoginTo', { lastLoginTo });
      }

      // Apply pagination
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Order by creation date
      queryBuilder.orderBy('client.createdAt', 'DESC');

      const [data, total] = await queryBuilder.getManyAndCount();

      return createPaginatedResult(data, total, page, limit);
    } catch (error) {
      this.logger.error(
        `Failed to fetch Client records: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Get Client record by ID
   */
  async findOne(id: string): Promise<MemberStatistics> {
    try {
      const clientData = await this.clientsRepository.findOne({
        where: { id }
      });

      if (!clientData) {
        throw new NotFoundException(`Client record with ID ${id} not found`);
      }

      return clientData;
    } catch (error) {
      this.logger.error(
        `Failed to fetch Client record: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Get Client record by account number
   */
  async findByAccountNumber(username: string): Promise<MemberStatistics> {
    try {
      const clientData = await this.clientsRepository.findOne({
        where: { username }
      });

      if (!clientData) {
        throw new NotFoundException(`Client record with account number ${username} not found`);
      }

      return clientData;
    } catch (error) {
      this.logger.error(
        `Failed to fetch Client record by account number: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Update Client record
   */
  async update(id: string, updateClientDto: UpdateClientDto): Promise<MemberStatistics> {
    try {
      const clientData = await this.findOne(id);

      // Check if account number is being changed and if it already exists
      if (updateClientDto.username && updateClientDto.username !== clientData.username) {
        const existingClient = await this.clientsRepository.findOne({
          where: { username: updateClientDto.username }
        });

        if (existingClient) {
          throw new BadRequestException(`Account number ${updateClientDto.username} already exists`);
        }
      }

      Object.assign(clientData, updateClientDto);
      const updatedClient = await this.clientsRepository.save(clientData);

      this.logger.log(
        `Updated Client record: ${updatedClient.username}`,
        'ClientsService'
      );

      return updatedClient;
    } catch (error) {
      this.logger.error(
        `Failed to update Client record: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Delete Client record (soft delete)
   */
  async remove(id: string, deletedBy: string): Promise<boolean> {
    try {
      const clientData = await this.findOne(id);

      clientData.isActive = false;
      clientData.deletedAt = new Date();
      clientData.deletedBy = deletedBy;
      await this.clientsRepository.save(clientData);

      this.logger.log(
        `Soft deleted Client record: ${clientData.username}`,
        'ClientsService'
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete Client record: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }

  /**
   * Bulk create Client records (for import)
   */
  async bulkCreate(clientDataList: CreateClientDto[]): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = { success: 0, failed: 0, errors: [] as string[] };

    for (const clientDto of clientDataList) {
      try {
        await this.create(clientDto);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Account ${clientDto.username}: ${error.message}`);
      }
    }

    this.logger.log(
      `Bulk create completed: ${results.success} success, ${results.failed} failed`,
      'ClientsService'
    );

    return results;
  }

  /**
   * Get statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const totalRecords = await this.clientsRepository.count();
      const activeRecords = await this.clientsRepository.count({ where: { isActive: true } });

      const totalDeposits = await this.clientsRepository
        .createQueryBuilder('client')
        .select('SUM(client.depositAmount)', 'total')
        .getRawOne();

      const totalWithdraws = await this.clientsRepository
        .createQueryBuilder('client')
        .select('SUM(client.withdrawAmount)', 'total')
        .getRawOne();

      return {
        totalRecords,
        activeRecords,
        inactiveRecords: totalRecords - activeRecords,
        totalDepositAmount: parseFloat(totalDeposits.total) || 0,
        totalWithdrawAmount: parseFloat(totalWithdraws.total) || 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Client statistics: ${error.message}`,
        error.stack,
        'ClientsService'
      );
      throw error;
    }
  }
}
