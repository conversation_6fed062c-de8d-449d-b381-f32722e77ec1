import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  Is<PERSON><PERSON>ber,
  IsBoolean,
  Min,
  IsDate,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateClientDto {
  @ApiProperty({
    description: 'Số tài khoản',
    example: 'BB33333',
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    description: 'Thời gian tham gia',
    example: '2025-05-05T10:30:00Z',
  })
  @IsDate({ message: 'joinTime must be a valid date' })
  @Transform(({ value }) => new Date(value))
  @Type(() => Date)
  joinTime: Date;

  @ApiProperty({
    description: 'Tên thật',
    example: 'NGUYEN VAN A',
  })
  @IsString()
  @IsNotEmpty()
  realName: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> nh<PERSON><PERSON> quyền trực tiế<PERSON>',
    example: 'gg001',
  })
  @IsString()
  @IsOptional()
  directFranchise?: string;

  @ApiPropertyOptional({
    description: 'Số lượng tiền gửi',
    example: 5,
    default: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 0)
  depositCount?: number = 0;

  @ApiPropertyOptional({
    description: 'Số tiền gửi',
    example: 1000000,
    default: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  depositAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Số tiền rút',
    example: 500000,
    default: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  withdrawAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Lần đăng nhập cuối cùng',
    example: '2025-05-07T15:30:00Z',
  })
  @IsDate({ message: 'lastLoginTime must be a valid date' })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  @Type(() => Date)
  lastLoginTime?: Date;

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Khách hàng VIP',
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true || value === 'true')
  isActive?: boolean = true;
}
