import { IsOptional, IsString, IsDate } from 'class-validator';
import {  Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from '@/common';

export class ClientQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo số tài khoản',
    example: 'BB333'
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên thật',
    example: 'NGUYEN'
  })
  @IsString()
  @IsOptional()
  realName?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo bên nhượng quyền',
    example: 'gg001'
  })
  @IsString()
  @IsOptional()
  directFranchise?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tham gia từ',
    example: '2025-01-01T00:00:00Z'
  })
  @IsDate({ message: 'joinTimeFrom must be a valid date' })
  @IsOptional()
  @Type(() => Date)
  joinTimeFrom?: Date;

  @ApiPropertyOptional({
    description: 'Thời gian tham gia đến',
    example: '2025-12-31T23:59:59Z'
  })
  @IsDate({ message: 'joinTimeTo must be a valid date' })
  @IsOptional()
  @Type(() => Date)
  joinTimeTo?: Date;

  @ApiPropertyOptional({
    description: 'Đăng nhập cuối từ',
    example: '2025-01-01T00:00:00Z'
  })
  @IsDate({ message: 'lastLoginTime must be a valid date' })
  @IsOptional()
  @Type(() => Date)
  lastLoginFrom?: Date;

  @ApiPropertyOptional({
    description: 'Đăng nhập cuối đến',
    example: '2025-12-31T23:59:59Z'
  })
  @IsDate({ message: 'lastLoginTime must be a valid date' })
  @IsOptional()
  @Type(() => Date)
  lastLoginTo?: Date;

}
