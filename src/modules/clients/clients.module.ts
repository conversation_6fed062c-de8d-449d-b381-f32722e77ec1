import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsService } from './clients.service';
import { ClientsController } from './clients.controller';
import { MemberStatistics } from '@/entities';
import { QueueModule } from '@/common/queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MemberStatistics]),
    QueueModule,
  ],
  controllers: [ClientsController],
  providers: [ClientsService],
  exports: [ClientsService],
})
export class ClientsModule {}
