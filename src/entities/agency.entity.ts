import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Member } from './member.entity';
import { Department } from './department.entity';

/**
 * Agency Entity
 * Represents agencies in the system
 */
@Entity('agencies')
export class Agency extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the agency',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the agency',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Agency contact email',
  })
  email?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Agency contact phone',
  })
  phone?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Agency address',
  })
  address?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the agency is active',
  })
  active: boolean;

  // Relationships
  @OneToMany(() => Member, (member) => member.agency)
  members: Member[];

  @OneToMany(() => Department, (department) => department.agency)
  departments: Department[];
}
