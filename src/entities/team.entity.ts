import { <PERSON>tity, Column, ManyToMany, OneToMany, JoinT<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { User } from './user.entity';

/**
 * Team Entity
 * Represents teams that can belong to multiple departments
 */
@Entity('teams')
export class Team extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the team',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the team',
  })
  description?: string;

  // Relationships
  @ManyToMany(() => Department, (department) => department.teams)
  @JoinTable({
    name: 'team_departments',
    joinColumn: {
      name: 'team_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'department_id',
      referencedColumnName: 'id',
    },
  })
  departments: Department[];

  @OneToMany(() => User, (user) => user.team)
  users: User[];
}
