import { Entity, Column, <PERSON>To<PERSON>ne, JoinColumn, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Member } from './member.entity';

export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
  TRANSFER = 'TRANSFER',
  BONUS = 'BONUS',
  COMMISSION = 'COMMISSION',
  REFUND = 'REFUND',
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

/**
 * Member Transaction Entity
 * Represents financial transactions for members
 */
@Entity('member_transactions')
@Index('IDX_member_transaction_member_id', ['memberId'])
@Index('IDX_member_transaction_type', ['type'])
@Index('IDX_member_transaction_status', ['status'])
@Index('IDX_member_transaction_amount', ['amount'])
export class MemberTransaction extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Member ID',
  })
  memberId: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
    comment: 'Type of transaction',
  })
  type: TransactionType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Transaction amount',
  })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Transaction fee',
  })
  fee: number;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
    comment: 'Transaction status',
  })
  status: TransactionStatus;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Transaction description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Reference number',
  })
  referenceNumber?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Transaction processed time',
  })
  processedAt?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional transaction data',
  })
  transactionData?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Member, (member) => member.transactions)
  @JoinColumn({ name: 'memberId' })
  member: Member;
}
