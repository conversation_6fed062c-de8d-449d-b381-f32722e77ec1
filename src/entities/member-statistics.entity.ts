import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity('member_statistics')
@Index('IDX_member_account_number', ['accountNumber'], { unique: true })
@Index('IDX_member_join_time', ['joinTime'])
@Index('IDX_member_last_login_time', ['lastLoginTime'])
export class MemberStatistics extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Số tài khoản'
  })
  accountNumber: string;

  @Column({
    type: 'timestamp',
    comment: 'Thời gian tham gia'
  })
  joinTime: Date;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tên thật'
  })
  realName: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Bên nhượng quyền trực tiếp'
  })
  directFranchise: string;

  @Column({
    type: 'int',
    default: 0,
    comment: '<PERSON><PERSON> lượng tiền gửi'
  })
  depositCount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Số tiền gửi'
  })
  depositAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Số tiền rút'
  })
  withdrawAmount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Lần đăng nhập cuối cùng'
  })
  lastLoginTime: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Ghi chú'
  })
  notes: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Trạng thái hoạt động'
  })
  isActive: boolean;
}
