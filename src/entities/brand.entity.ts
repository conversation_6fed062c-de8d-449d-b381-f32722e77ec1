import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { InvoiceProposals } from './invoice-proposals.entity';

/**
 * Brand Entity
 * Represents brands in the system
 */
@Entity('brands')
export class Brand extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the brand',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the brand',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Brand logo URL or path',
  })
  logo?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Brand website URL',
  })
  website?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the brand is active',
  })
  active: boolean;

  // Relationships
  @OneToMany(() => InvoiceProposals, (proposal) => proposal.brand)
  proposals: InvoiceProposals[];
}
