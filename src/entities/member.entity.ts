import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Agency } from './agency.entity';
import { MemberTransaction } from './member-transaction.entity';

/**
 * Member Entity
 * Represents members in the system
 */
@Entity('members')
@Index('IDX_member_friend_id', ['friendId'], { unique: true })
@Index('IDX_member_fullname', ['fullname'])
@Index('IDX_member_level', ['level'])
export class Member extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Friend ID of the member',
  })
  friendId: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Full name of the member',
  })
  fullname: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Member level',
  })
  level?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Email address',
  })
  email?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Phone number',
  })
  phone?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Address',
  })
  address?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Join date',
  })
  joinDate?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Last login time',
  })
  lastLoginTime?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the member is active',
  })
  active: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Agency ID',
  })
  agencyId?: string;

  // Relationships
  @ManyToOne(() => Agency, (agency) => agency.members, { nullable: true })
  @JoinColumn({ name: 'agencyId' })
  agency?: Agency;

  @OneToMany(() => MemberTransaction, (transaction) => transaction.member)
  transactions: MemberTransaction[];
}
