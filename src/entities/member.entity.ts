import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Agency } from './agency.entity';
import { MemberTransaction } from './member-transaction.entity';

/**
 * Member Entity
 * Represents members in the system
 */
@Entity('members')
@Index('IDX_member_fullname', ['fullname'])
@Index('IDX_member_username', ['username'])
export class Member extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Số tài khoản'
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Họ tên'
  })
  fullname: string;

  @Column({
    type: 'timestamp',
    comment: 'Thời gian tham gia'
  })
  joinTime: Date;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tên thật'
  })
  realName: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> nh<PERSON>ợng quyền trực tiếp'
  })
  directFranchise: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Số lượng tiền gửi'
  })
  depositCount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Số tiền gửi'
  })
  depositAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Số tiền rút'
  })
  withdrawAmount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Lần đăng nhập cuối cùng'
  })
  lastLoginTime: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Ghi chú'
  })
  notes: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Trạng thái hoạt động'
  })
  isActive: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Agency ID',
  })
  agencyId?: string;

  // Relationships
  @ManyToOne(() => Agency, (agency) => agency.members, { nullable: true })
  @JoinColumn({ name: 'agencyId' })
  agency?: Agency;

  @OneToMany(() => MemberTransaction, (transaction) => transaction.member)
  transactions: MemberTransaction[];
}