import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, ManyToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Team } from './team.entity';
import { User } from './user.entity';
import { Agency } from './agency.entity';

/**
 * Department Entity
 * Represents departments in the organization
 */
@Entity('departments')
export class Department extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the department',
  })
  name: string;

  @ManyToOne(() => Agency, (agency) => agency.departments, { nullable: true })
  @JoinColumn({ name: 'agencyId' })
  agency?: Agency;

  // Relationships
  @ManyToMany(() => Team, (team) => team.departments)
  teams: Team[];

  @ManyToMany(() => User, (user) => user.departments)
  users: User[];
}
