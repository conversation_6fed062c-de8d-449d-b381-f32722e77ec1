import { Entity, Column, ManyToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Team } from './team.entity';
import { User } from './user.entity';

/**
 * Department Entity
 * Represents departments in the organization
 */
@Entity('departments')
export class Department extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the department',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Agency associated with the department',
  })
  agency?: string;

  // Relationships
  @ManyToMany(() => Team, (team) => team.departments)
  teams: Team[];

  @ManyToMany(() => User, (user) => user.departments)
  users: User[];
}
