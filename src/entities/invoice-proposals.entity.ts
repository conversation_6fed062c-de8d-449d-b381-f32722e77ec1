import { Entity, Column, <PERSON>To<PERSON><PERSON>, Join<PERSON>olumn, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

import { Department } from './department.entity';
import { Team } from './team.entity';
import { Brand } from './brand.entity';

export enum ProposalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  IN_REVIEW = 'IN_REVIEW',
  CANCELLED = 'CANCELLED',
}

/**
 * Invoice Proposals Entity
 * Represents invoice proposals in the system
 */
@Entity('invoice_proposals')
@Index('IDX_invoice_proposal_brand_id', ['brandId'])
@Index('IDX_invoice_proposal_department_id', ['departmentId'])
@Index('IDX_invoice_proposal_team_id', ['teamId'])
@Index('IDX_invoice_proposal_status', ['status'])
export class InvoiceProposals extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Brand ID',
  })
  brandId: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Department ID',
  })
  departmentId: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Team ID',
  })
  teamId: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Requester name or ID',
  })
  requester: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Proposal name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Proposal description',
  })
  description?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Partner information',
  })
  partnerInfo?: string;

  @Column({
    type: 'int',
    default: 1,
    comment: 'Quantity',
  })
  quantity: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Amount',
  })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Fee',
  })
  fee: number;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Payment information',
  })
  paymentInfo?: string;

  @Column({
    type: 'enum',
    enum: ProposalStatus,
    default: ProposalStatus.PENDING,
    comment: 'Proposal status',
  })
  status: ProposalStatus;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Resource name',
  })
  resourceName?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Proposer ID',
  })
  proposerId?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Unit price',
  })
  unitPrice?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Total amount',
  })
  totalAmount?: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Reviewed by',
  })
  reviewedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Reviewed at',
  })
  reviewedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Rejection reason',
  })
  rejectionReason?: string;

  // Relationships
  @ManyToOne(() => Brand, (brand) => brand.proposals)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'departmentId' })
  department?: Department;

  @ManyToOne(() => Team, { nullable: true })
  @JoinColumn({ name: 'teamId' })
  team?: Team;
}
